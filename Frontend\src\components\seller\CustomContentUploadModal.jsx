import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { FaTimes, FaUpload, FaCheckCircle } from 'react-icons/fa';
import { FiUpload } from 'react-icons/fi';
import useModalScrollLock from '../../hooks/useModalScrollLock';
import { uploadContentFile } from '../../redux/slices/sellerDashboardSlice';
import api from '../../services/api';
import '../../styles/CustomContentUploadModal.css';

const CustomContentUploadModal = ({ isOpen, onClose, request, onSuccess }) => {
  useModalScrollLock(isOpen);
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    title: request?.title || '',
    description: '',
    category: 'General',
    aboutCoach: '',
    strategicContent: '',
    language: 'English',
    submissionMessage: ''
  });

  const [uploadedFile, setUploadedFile] = useState(null);
  const [fileUrl, setFileUrl] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setUploadedFile(file);
    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formDataUpload = new FormData();
      formDataUpload.append('file', file);
      formDataUpload.append('type', 'content');

      const result = await dispatch(uploadContentFile({
        formData: formDataUpload,
        onProgress: (progress) => setUploadProgress(progress)
      })).unwrap();

      setFileUrl(result.data.fileUrl);
      toast.success('File uploaded successfully!');
    } catch (error) {
      console.error('Upload error:', error);
      toast.error(error.message || 'Failed to upload file');
      setUploadedFile(null);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    if (!formData.aboutCoach.trim()) {
      newErrors.aboutCoach = 'About coach information is required';
    }
    if (!formData.strategicContent.trim()) {
      newErrors.strategicContent = 'Strategic content description is required';
    }
    if (!fileUrl) {
      newErrors.file = 'Please upload a file';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // First create the content
      const contentData = {
        title: formData.title,
        description: formData.description,
        sport: request.sport,
        category: formData.category,
        contentType: request.contentType,
        fileUrl: fileUrl,
        fileSize: uploadedFile?.size || 0,
        difficulty: 'Intermediate',
        coachName: `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'Professional Coach',
        aboutCoach: formData.aboutCoach,
        strategicContent: formData.strategicContent,
        language: formData.language,
        prerequisites: [],
        learningObjectives: [],
        equipment: [],
        saleType: 'Fixed',
        price: request.sellerResponse?.price || 0,
        status: 'Published',
        visibility: 'Public',
        isCustomContent: true,
        customRequestId: request._id
      };

      const contentResponse = await api.post('/content', contentData);
      const createdContent = contentResponse.data.data;

      // Then submit the content to the custom request
      await api.put(`/requests/${request._id}/submit-content`, {
        contentId: createdContent._id,
        submissionMessage: formData.submissionMessage || 'Your custom content has been completed and is ready for access.'
      });

      setShowSuccess(true);
      toast.success('Custom content submitted successfully!');

      // Call onSuccess callback after a delay
      setTimeout(() => {
        onSuccess && onSuccess();
        handleClose();
      }, 2000);

    } catch (error) {
      console.error('Submission error:', error);
      toast.error(error.response?.data?.message || 'Failed to submit content');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (isUploading || isSubmitting) return;

    setFormData({
      title: request?.title || '',
      description: '',
      category: 'General',
      aboutCoach: '',
      strategicContent: '',
      language: 'English',
      submissionMessage: ''
    });
    setUploadedFile(null);
    setFileUrl('');
    setErrors({});
    setShowSuccess(false);
    onClose();
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget && !isUploading && !isSubmitting) {
      handleClose();
    }
  };

  if (!isOpen || !request) return null;

  if (showSuccess) {
    return (
      <div className="custom-content-modal-overlay" onClick={handleOverlayClick}>
        <div className="custom-content-modal success-modal">
          <div className="success-content">
            <FaCheckCircle className="success-icon" />
            <h2>Content Submitted Successfully!</h2>
            <p>Your custom content has been submitted to the buyer.</p>
            <p>The buyer will be notified and can now access the content.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="custom-content-modal-overlay" onClick={handleOverlayClick}>
      <div className="custom-content-modal">
        <div className="modal-header">
          <h2>Upload Custom Content</h2>
          <button
            className="close-btn"
            onClick={handleClose}
            disabled={isUploading || isSubmitting}
          >
            <FaTimes />
          </button>
        </div>

        <div className="modal-body">
          <div className="request-info">
            <h3>Request: {request.title}</h3>
            <p>Content Type: {request.contentType}</p>
            <p>Budget: ${request.sellerResponse?.price || request.budget}</p>
          </div>

          <form onSubmit={handleSubmit} className="upload-form">
            <div className="form-group">
              <label htmlFor="title">Content Title *</label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className={errors.title ? 'error' : ''}
                placeholder="Enter content title"
              />
              {errors.title && <span className="error-message">{errors.title}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="description">Description *</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className={errors.description ? 'error' : ''}
                placeholder="Describe your content"
                rows="3"
              />
              {errors.description && <span className="error-message">{errors.description}</span>}
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="category">Category</label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                >
                  <option value="General">General</option>
                  <option value="Beginner">Beginner</option>
                  <option value="Intermediate">Intermediate</option>
                  <option value="Advanced">Advanced</option>
                </select>
              </div>

              {/* <div className="form-group">
                <label htmlFor="language">Language</label>
                <select
                  id="language"
                  name="language"
                  value={formData.language}
                  onChange={handleInputChange}
                >
                  <option value="English">English</option>
                  <option value="Spanish">Spanish</option>
                  <option value="French">French</option>
                  <option value="German">German</option>
                  <option value="Other">Other</option>
                </select>
              </div> */}
            </div>

            <div className="form-group">
              <label htmlFor="aboutCoach">About Coach *</label>
              <textarea
                id="aboutCoach"
                name="aboutCoach"
                value={formData.aboutCoach}
                onChange={handleInputChange}
                className={errors.aboutCoach ? 'error' : ''}
                placeholder="Tell the buyer about your expertise"
                rows="2"
              />
              {errors.aboutCoach && <span className="error-message">{errors.aboutCoach}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="strategicContent">Strategic Content *</label>
              <textarea
                id="strategicContent"
                name="strategicContent"
                value={formData.strategicContent}
                onChange={handleInputChange}
                className={errors.strategicContent ? 'error' : ''}
                placeholder="Describe the strategic value of this content"
                rows="2"
              />
              {errors.strategicContent && <span className="error-message">{errors.strategicContent}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="file-upload">Upload File *</label>
              <div className="file-upload-area">
                <input
                  type="file"
                  id="file-upload"
                  onChange={handleFileUpload}
                  accept={request.contentType === 'Video' ? 'video/*' : request.contentType === 'PDF' ? '.pdf' : '*/*'}
                  style={{ display: 'none' }}
                  disabled={isUploading}
                />
                <label htmlFor="file-upload" className={`file-upload-label ${isUploading ? 'uploading' : ''}`}>
                  <FiUpload className="upload-icon" />
                  <div className="upload-text">
                    {isUploading ? (
                      <div>
                        <p>Uploading... {uploadProgress}%</p>
                        <div className="progress-bar">
                          <div
                            className="progress-fill"
                            style={{ width: `${uploadProgress}%` }}
                          />
                        </div>
                      </div>
                    ) : uploadedFile ? (
                      <p>✓ {uploadedFile.name}</p>
                    ) : (
                      <p>Click to upload {request.contentType.toLowerCase()} file</p>
                    )}
                  </div>
                </label>
              </div>
              {errors.file && <span className="error-message">{errors.file}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="submissionMessage">Message to Buyer</label>
              <textarea
                id="submissionMessage"
                name="submissionMessage"
                value={formData.submissionMessage}
                onChange={handleInputChange}
                placeholder="Optional message to include with your content submission"
                rows="2"
              />
            </div>

            <div className="form-actions">
              <button
                type="button"
                className="btn-secondary"
                onClick={handleClose}
                disabled={isUploading || isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary"
                disabled={isUploading || isSubmitting || !fileUrl}
              >
                {isSubmitting ? 'Submitting...' : 'Submit Content'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CustomContentUploadModal;
